import "/src/style.css?t=1749482376171";
import { MapManager } from "/src/map.ts";
import { LayerManager } from "/src/layers.ts";
document.addEventListener("DOMContentLoaded", () => {
  const mapManager = new MapManager("map");
  const layerManager = new LayerManager(
    mapManager,
    "layer-control",
    "legend-content"
  );
  setupEventListeners(mapManager, layerManager);
  window.addEventListener("beforeunload", () => {
    layerManager.destroy();
  });
});
function setupEventListeners(mapManager, layerManager) {
  setupDraggablePanels();
  const sidebar = document.getElementById("sidebar");
  const legendPanel = document.getElementById("legend-panel");
  const searchPanel = document.getElementById("search-panel");
  const searchInput = document.getElementById("search-input");
  const searchResults = document.getElementById("search-results");
  const layersToggleBtn = document.getElementById("layers-toggle");
  const legendToggleBtn = document.getElementById("legend-toggle");
  const homeButton = document.getElementById("home-button");
  const searchToggleBtn = document.getElementById("search-toggle");
  const sidebarCloseBtn = document.getElementById("sidebar-close");
  const legendCloseBtn = document.getElementById("legend-close");
  const searchCloseBtn = document.getElementById("search-close");
  layersToggleBtn?.addEventListener("click", () => {
    sidebar?.classList.toggle("hidden");
  });
  sidebarCloseBtn?.addEventListener("click", () => {
    sidebar?.classList.add("hidden");
  });
  legendToggleBtn?.addEventListener("click", () => {
    legendPanel?.classList.toggle("hidden");
    if (!legendPanel?.classList.contains("hidden")) {
      layerManager.updateLegend();
    }
  });
  legendCloseBtn?.addEventListener("click", () => {
    legendPanel?.classList.add("hidden");
  });
  homeButton?.addEventListener("click", () => {
    mapManager.zoomToHome();
  });
  searchToggleBtn?.addEventListener("click", () => {
    searchPanel?.classList.toggle("hidden");
    if (!searchPanel?.classList.contains("hidden")) {
      searchInput?.focus();
    }
  });
  searchCloseBtn?.addEventListener("click", () => {
    searchPanel?.classList.add("hidden");
  });
  let searchTimeout = null;
  searchInput?.addEventListener("input", (e) => {
    const query = e.target.value.trim();
    if (searchTimeout) {
      window.clearTimeout(searchTimeout);
    }
    if (query.length > 2) {
      searchTimeout = window.setTimeout(() => {
        performSearch(query);
      }, 500);
    } else if (searchResults) {
      searchResults.innerHTML = "";
    }
  });
  async function performSearch(query) {
    if (!searchResults) return;
    searchResults.innerHTML = '<div class="loading">Searching...</div>';
    try {
      const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}`);
      const data = await response.json();
      if (data.length === 0) {
        searchResults.innerHTML = '<div class="no-layers">No results found</div>';
        return;
      }
      searchResults.innerHTML = "";
      data.slice(0, 5).forEach((result) => {
        const resultItem = document.createElement("div");
        resultItem.className = "search-result-item";
        resultItem.textContent = result.display_name;
        resultItem.addEventListener("click", () => {
          mapManager.getMap().setView([parseFloat(result.lat), parseFloat(result.lon)], 13);
          searchPanel?.classList.add("hidden");
        });
        searchResults.appendChild(resultItem);
      });
    } catch (error) {
      console.error("Error searching locations:", error);
      searchResults.innerHTML = '<div class="error">Error searching locations</div>';
    }
  }
}
function setupDraggablePanels() {
  const draggableElements = [
    { panel: "sidebar", header: ".sidebar-header" },
    { panel: "legend-panel", header: ".legend-header" },
    { panel: "search-panel", header: ".search-header" }
  ];
  draggableElements.forEach(({ panel, header }) => {
    const panelElement = document.getElementById(panel);
    const headerElement = panelElement?.querySelector(header);
    if (!panelElement || !headerElement) return;
    let isDragging = false;
    let startX = 0;
    let startY = 0;
    let lastX = 0;
    let lastY = 0;
    let rafId = null;
    if (!panelElement.style.transform) {
      const rect = panelElement.getBoundingClientRect();
      setTransform(rect.left, rect.top);
    } else {
      const style = window.getComputedStyle(panelElement);
      const matrix = new DOMMatrix(style.transform);
      lastX = matrix.e;
      lastY = matrix.f;
    }
    headerElement.addEventListener("mousedown", startDrag);
    document.addEventListener("mousemove", drag);
    document.addEventListener("mouseup", stopDrag);
    headerElement.addEventListener("touchstart", startDragTouch, { passive: false });
    document.addEventListener("touchmove", dragTouch, { passive: false });
    document.addEventListener("touchend", stopDrag);
    function startDrag(e) {
      if (e.target.tagName === "BUTTON" || e.target.tagName === "INPUT" || e.target.closest("button")) {
        return;
      }
      startX = e.clientX;
      startY = e.clientY;
      const style = window.getComputedStyle(panelElement);
      const matrix = new DOMMatrix(style.transform);
      lastX = matrix.e;
      lastY = matrix.f;
      isDragging = true;
      panelElement.classList.add("dragging");
      e.preventDefault();
    }
    function startDragTouch(e) {
      if (e.target.tagName === "BUTTON" || e.target.tagName === "INPUT" || e.target.closest("button")) {
        return;
      }
      const touch = e.touches[0];
      startX = touch.clientX;
      startY = touch.clientY;
      const style = window.getComputedStyle(panelElement);
      const matrix = new DOMMatrix(style.transform);
      lastX = matrix.e;
      lastY = matrix.f;
      isDragging = true;
      panelElement.classList.add("dragging");
      e.preventDefault();
    }
    function drag(e) {
      if (!isDragging) return;
      e.preventDefault();
      const deltaX = e.clientX - startX;
      const deltaY = e.clientY - startY;
      const newX = lastX + deltaX;
      const newY = lastY + deltaY;
      if (rafId === null) {
        rafId = requestAnimationFrame(() => {
          setTransform(newX, newY);
          rafId = null;
        });
      }
    }
    function dragTouch(e) {
      if (!isDragging) return;
      e.preventDefault();
      const touch = e.touches[0];
      const deltaX = touch.clientX - startX;
      const deltaY = touch.clientY - startY;
      const newX = lastX + deltaX;
      const newY = lastY + deltaY;
      if (rafId === null) {
        rafId = requestAnimationFrame(() => {
          setTransform(newX, newY);
          rafId = null;
        });
      }
    }
    function setTransform(x, y) {
      panelElement.style.transform = `translate3d(${Math.round(x)}px, ${Math.round(y)}px, 0)`;
      panelElement.style.right = "auto";
    }
    function stopDrag() {
      if (!isDragging) return;
      isDragging = false;
      panelElement.classList.remove("dragging");
      const style = window.getComputedStyle(panelElement);
      const matrix = new DOMMatrix(style.transform);
      lastX = matrix.e;
      lastY = matrix.f;
    }
  });
}

//# sourceMappingURL=data:application/json;base64,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