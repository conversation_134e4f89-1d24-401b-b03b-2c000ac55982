<!DOCTYPE html>
<!-- saved from url=(0022)http://localhost:5173/ -->
<html lang="en"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <script type="module" src="./Geoportal Tabacay Aguilan_files/client"></script>

    
    <link rel="icon" type="image/svg+xml" href="http://localhost:5173/vite.svg">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Geoportal Tabacay Aguilan</title>
    <link rel="stylesheet" href="./Geoportal Tabacay Aguilan_files/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin="">
    <link rel="stylesheet" href="./Geoportal Tabacay Aguilan_files/all.min.css">
  <style type="text/css" data-vite-dev-id="C:/Users/<USER>/Documents/Geoportal/geoportal-tabacay-aguilan/src/style.css">* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body, html {
  font-family: Arial, Helvetica, sans-serif;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

#app {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header Styles */
.geoportal-header {
  background: linear-gradient(90deg, #9f3030, #84a5ff);
  color: white;
  padding: 15px 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1001;
  position: relative;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  gap: 20px;
}

.header-logo {
  flex-shrink: 0;
}

.institution-logo {
  height: 60px;
  width: auto;
  object-fit: contain;
  padding: 5px;
  border-radius: 4px;
}

.header-title {
  flex: 1;
  text-align: center;
  min-width: 0;
}

.header-title h1 {
  margin: 0 0 5px 0;
  font-size: 24px;
  font-weight: bold;
  color: white;
}

.header-title p {
  margin: 0;
  font-size: 14px;
  color: #adb5bd;
  font-weight: normal;
}

.geoportal-container {
  display: flex;
  width: 100%;
  flex: 1;
  position: relative;
  min-height: 0;
}

#map-container {
  flex: 1;
  position: relative;
  height: 100%;
}

#map {
  width: 100%;
  height: 100%;
}

.sidebar {
  position: absolute;
  top: 10px;
  left: 90px;
  width: 320px; /* Slightly wider to accommodate wrapped text */
  max-height: calc(100% - 20px);
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  overflow-y: auto;
  /* Remove transition for smoother dragging */
  will-change: transform; /* Hint for hardware acceleration */
}

.sidebar-header {
  padding: 15px;
  background-color: #343a40;
  color: white;
  border-bottom: 1px solid #495057;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: grab; /* Better cursor for draggable elements */
  user-select: none; /* Prevent text selection during drag */
}

.sidebar-content {
  padding: 10px;
  max-height: 70vh;
  overflow-y: auto;
  font-size: 12px; /* Ensure consistent font size throughout the sidebar */
}

.hidden {
  display: none;
}

/* Draggable elements */
.draggable-header {
  position: relative;
  padding-left: 25px !important; /* Make room for the drag handle */
  cursor: grab; /* Better cursor for draggable elements */
  user-select: none; /* Prevent text selection during drag */
}

.draggable-header::before {
  content: '⋮⋮';
  position: absolute;
  left: 8px;
  opacity: 0.5;
  font-size: 12px;
  pointer-events: none;
}

.draggable-header h2 {
  margin: 0;
  font-size: 16px;
}

.draggable-header.dragging {
  cursor: grabbing !important;
}

/* Ensure panels can be dragged anywhere */
.sidebar, .legend-panel, .search-panel {
  position: absolute;
  z-index: 1500; /* Ensure panels are above other elements */
  will-change: transform; /* Hint to browser to use hardware acceleration */
  transition: box-shadow 0.2s ease; /* Smooth transition for shadow effect */
}

/* Style for panels while being dragged */
.dragging {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2); /* Enhanced shadow while dragging */
  cursor: grabbing !important;
  opacity: 0.95; /* Slight transparency while dragging */
}

/* Floating Action Menu */
.floating-menu {
  position: absolute;
  top: 20px;
  left: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 1000;
}

.menu-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: white;
  border: 2px solid rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.menu-button:hover {
  background-color: #f8f9fa;
  transform: scale(1.05);
}

.menu-button:active {
  transform: scale(0.95);
}

.close-button {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
}

.layer-item {
  margin-bottom: 10px;
  padding: 8px;
  background-color: #fff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  display: flex;
  flex-wrap: nowrap; /* Prevent wrapping of the flex items */
  align-items: flex-start; /* Align with the top of wrapped text */
}

.layer-item label {
  margin-left: 8px;
  flex: 1;
  min-width: 0; /* Allow label to shrink below its content size */
  cursor: pointer;
  font-size: 12px; /* Smaller font size */
  line-height: 1.3; /* Better line height for readability */
  word-wrap: break-word; /* Ensure text wraps */
  overflow-wrap: break-word; /* Modern browsers */
  hyphens: auto; /* Add hyphens for very long words */
  white-space: normal; /* Allow text to wrap */
  display: inline-block; /* Better text wrapping */
}

.layer-item input[type="checkbox"] {
  cursor: pointer;
  margin-top: 2px; /* Align checkbox with the first line of text */
  flex-shrink: 0; /* Prevent checkbox from shrinking */
}

/* Layer info icon */
.layer-info-icon {
  margin-left: 5px;
  color: #6c757d;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 2px; /* Align with first line of text */
  min-width: 16px; /* Ensure consistent width */
  width: 16px; /* Fixed width */
  height: 16px;
  position: relative; /* For proper positioning */
  z-index: 10; /* Ensure it's above other elements */
  flex-shrink: 0; /* Prevent icon from shrinking */
  align-self: flex-start; /* Align to the top */
}

.layer-info-icon:hover {
  color: #007bff;
}

/* Layer tooltip */
.layer-tooltip {
  position: fixed; /* Fixed position relative to viewport */
  background-color: #fff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 10px;
  max-width: 300px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 2000; /* Higher than other elements */
  font-size: 12px;
  line-height: 1.4;
  color: #333;
  word-wrap: break-word;
  overflow-wrap: break-word;
  pointer-events: none; /* Allow mouse events to pass through */
  opacity: 0.95; /* Slight transparency */
  transition: opacity 0.2s ease; /* Smooth transition */
  max-height: 200px; /* Limit height */
  overflow-y: auto; /* Add scrollbar if needed */
}

/* Layer Group Styles */
.layer-group {
  margin-bottom: 10px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  overflow: hidden;
}

.layer-group-header {
  padding: 8px;
  display: flex;
  flex-wrap: nowrap; /* Prevent wrapping of flex items */
  align-items: flex-start; /* Align with the top of wrapped text */
  background-color: #e9ecef;
  cursor: pointer;
  user-select: none;
}

.layer-group-header:hover {
  background-color: #dee2e6;
}

.layer-group-toggle {
  margin-right: 5px;
  min-width: 16px; /* Use min-width instead of width */
  width: 16px; /* Fixed width */
  height: 16px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  user-select: none;
  margin-top: 2px; /* Align with first line of text */
  flex-shrink: 0; /* Prevent icon from shrinking */
}

.layer-group-toggle.expanded {
  color: #495057;
}

.layer-group-toggle.collapsed {
  color: #6c757d;
}

.layer-group-header input[type="checkbox"] {
  margin-right: 8px;
  cursor: pointer;
  margin-top: 2px; /* Align checkbox with the first line of text */
  flex-shrink: 0; /* Prevent checkbox from shrinking */
}

.layer-group-header label {
  flex: 1;
  min-width: 0; /* Allow label to shrink below its content size */
  cursor: pointer;
  font-size: 12px; /* Smaller font size */
  font-weight: bold; /* Make group headers bold to distinguish them */
  line-height: 1.3; /* Better line height for readability */
  word-wrap: break-word; /* Ensure text wraps */
  overflow-wrap: break-word; /* Modern browsers */
  hyphens: auto; /* Add hyphens for very long words */
  white-space: normal; /* Allow text to wrap */
  display: inline-block; /* Better text wrapping */
}

.layer-group-children {
  padding: 0 0 5px 0;
  width: 100%; /* Ensure full width */
}

.layer-group-children .layer-item {
  margin: 5px 10px;
  border-radius: 3px;
  padding: 6px; /* Slightly smaller padding for nested items */
  width: calc(100% - 20px); /* Account for margins */
  box-sizing: border-box; /* Include padding in width calculation */
}

.legend-panel {
  position: absolute;
  top: 90px;
  left: 80%;
  width: 250px;
  max-height: calc(100% - 20px);
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  overflow-y: auto;
  /* Remove transition for smoother dragging */
  will-change: transform; /* Hint for hardware acceleration */
}

.legend-header {
  padding: 15px;
  background-color: #343a40;
  color: white;
  border-bottom: 1px solid #495057;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: grab; /* Better cursor for draggable elements */
  user-select: none; /* Prevent text selection during drag */
}

.legend-content {
  padding: 10px;
  max-height: 70vh;
  overflow-y: auto;
  font-size: 12px; /* Match layer panel text size */
}

/* Search Panel */
.search-panel {
  position: absolute;
  top: 10px;
  left: calc(50% - 250px); /* Center without using transform */
  width: 80%;
  max-width: 500px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  /* Remove transition for smoother dragging */
  will-change: transform; /* Hint for hardware acceleration */
}

.search-header {
  padding: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
  border-bottom: 1px solid #dee2e6;
  cursor: grab; /* Better cursor for draggable elements */
  background-color: #343a40;
  color: white;
  user-select: none; /* Prevent text selection during drag */
}

#search-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
}

.search-results {
  max-height: 300px;
  overflow-y: auto;
  padding: 10px;
}

.search-result-item {
  padding: 8px 12px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
}

.search-result-item:hover {
  background-color: #f1f1f1;
}

.search-result-item:last-child {
  border-bottom: none;
}

.legend-item {
  margin-bottom: 15px;
  display: flex;
  flex-direction: column;
}

.legend-item h3 {
  font-size: 12px; /* Match layer panel text size */
  margin-bottom: 8px;
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 5px;
  word-wrap: break-word; /* Ensure text wraps */
  overflow-wrap: break-word; /* Modern browsers */
  hyphens: auto; /* Add hyphens for very long words */
  line-height: 1.3; /* Better line height for readability */
  white-space: normal; /* Allow text to wrap */
}

.legend-symbol {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.legend-symbol-icon {
  width: 20px;
  height: 20px;
  margin-right: 10px;
}

.legend-symbol-label {
  font-size: 12px;
  word-wrap: break-word;
  overflow-wrap: break-word;
  flex: 1;
  min-width: 0;
}

/* Legend image and controls container */
.legend-image-container {
  display: flex;
  flex-direction: row; /* Changed to row to place image and controls side by side */
  align-items: flex-start;
  margin-bottom: 10px;
  width: 100%;
  gap: 10px; /* Add space between image and controls */
}

.legend-image {
  max-width: 120px; /* Limit image width */
  max-height: 150px; /* Limit image height */
  object-fit: contain; /* Maintain aspect ratio */
  margin-bottom: 0;
}

/* Controls container */
.legend-controls {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0; /* Allow container to shrink */
}

/* Opacity slider container */
.opacity-slider-container {
  display: flex;
  align-items: center;
  margin-top: 5px;
  width: 100%;
}

.opacity-slider-icon {
  color: #6c757d;
  font-size: 14px;
  margin: 0 5px;
}

.opacity-slider-icon.faded {
  opacity: 0.3;
}

.opacity-slider {
  flex: 1;
  height: 5px;
  -webkit-appearance: none;
  appearance: none;
  background: #dee2e6;
  outline: none;
  border-radius: 3px;
  margin: 0 5px;
}

.opacity-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 12px;
  height: 12px;
  background: #007bff;
  border-radius: 50%;
  cursor: pointer;
}

.opacity-slider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  background: #007bff;
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

/* Opacity input container */
.opacity-input-container {
  display: flex;
  align-items: center;
  margin-top: 8px;
  width: 100%;
}

/* Opacity input field */
.opacity-input {
  width: 60px;
  height: 24px;
  padding: 2px 5px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 12px;
  text-align: center;
  margin: 0 5px;
}

.opacity-input:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Loading and error messages */
.loading, .error, .no-layers {
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 10px;
  text-align: center;
}

.loading {
  background-color: #e9f5ff;
  color: #0066cc;
  border: 1px solid #b3d7ff;
}

.error {
  background-color: #ffebee;
  color: #c62828;
  border: 1px solid #ffcdd2;
}

.no-layers {
  background-color: #fff9e6;
  color: #856404;
  border: 1px solid #ffeeba;
}

/* Custom positioning for Leaflet controls */
.leaflet-top.leaflet-left {
  margin-top: 200px; /* Adjust this value based on the height of your floating menu */
}

/* Footer Styles */
.geoportal-footer {
  background-color: #343a40;
  color: white;
  padding: 20px;
  text-align: center;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1001;
  position: relative;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
}

.footer-project-text {
  margin: 0 0 10px 0;
  font-size: 14px;
  line-height: 1.4;
  color: #f8f9fa;
  font-weight: 500;
}

.footer-rights {
  margin: 0;
  font-size: 12px;
  color: #adb5bd;
}

.footer-link {
  color: #17a2b8;
  text-decoration: none;
  font-weight: bold;
  transition: color 0.2s ease;
}

.footer-link:hover {
  color: #20c997;
  text-decoration: underline;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .geoportal-container {
    flex-direction: column;
  }

  .sidebar, .legend-panel {
    width: 100%;
    height: auto;
    max-height: 200px;
  }

  .header-content {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .header-title h1 {
    font-size: 20px;
  }

  .header-title p {
    font-size: 12px;
  }

  .institution-logo {
    height: 50px;
  }

  .footer-project-text {
    font-size: 12px;
  }

  .footer-rights {
    font-size: 11px;
  }
}
</style></head>
  <body class="">
    <div id="app">
      <!-- Header -->
      <header class="geoportal-header">
        <div class="header-content">
          <div class="header-logo">
            <img src="./Geoportal Tabacay Aguilan_files/logo-u.png" alt="UCACUE - Universidad Católica de Cuenca" class="institution-logo">
          </div>
          <div class="header-title">
            <h1>Geoportal Tabacay Aguilán</h1>
            <p>Microcuenca de los Ríos Tabacay y Aguilán - Azogues</p>
          </div>
          <div class="header-logo">
            <img src="./Geoportal Tabacay Aguilan_files/Emapal.png" alt="EMAPAL EP - Empresa Pública Municipal" class="institution-logo">
          </div>
        </div>
      </header>

      <div class="geoportal-container">
        <div id="map-container">
          <div id="map" class="leaflet-container leaflet-touch leaflet-retina leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom" tabindex="0" style="position: relative;"><div class="leaflet-pane leaflet-map-pane" style="transform: translate3d(-105px, 47.8779px, 0px);"><div class="leaflet-pane leaflet-tile-pane"><div class="leaflet-layer " style="z-index: 1; opacity: 1;"><div class="leaflet-tile-container leaflet-zoom-animated" style="z-index: 18; transform: translate3d(-15px, -118px, 0px) scale(0.5);"></div><div class="leaflet-tile-container leaflet-zoom-animated" style="z-index: 18; transform: translate3d(-1012px, -289px, 0px) scale(2);"></div><div class="leaflet-tile-container leaflet-zoom-animated" style="z-index: 19; transform: translate3d(-1012px, -289px, 0px) scale(1);"><img alt="" src="./Geoportal Tabacay Aguilan_files/259.png" class="leaflet-tile leaflet-tile-loaded" style="width: 256px; height: 256px; transform: translate3d(1762px, 446px, 0px); opacity: 1;"><img alt="" src="./Geoportal Tabacay Aguilan_files/258.png" class="leaflet-tile leaflet-tile-loaded" style="width: 256px; height: 256px; transform: translate3d(1762px, 190px, 0px); opacity: 1;"><img alt="" src="./Geoportal Tabacay Aguilan_files/259(1).png" class="leaflet-tile leaflet-tile-loaded" style="width: 256px; height: 256px; transform: translate3d(1506px, 446px, 0px); opacity: 1;"><img alt="" src="./Geoportal Tabacay Aguilan_files/259(2).png" class="leaflet-tile leaflet-tile-loaded" style="width: 256px; height: 256px; transform: translate3d(2018px, 446px, 0px); opacity: 1;"><img alt="" src="./Geoportal Tabacay Aguilan_files/260.png" class="leaflet-tile leaflet-tile-loaded" style="width: 256px; height: 256px; transform: translate3d(1762px, 702px, 0px); opacity: 1;"><img alt="" src="./Geoportal Tabacay Aguilan_files/258(1).png" class="leaflet-tile leaflet-tile-loaded" style="width: 256px; height: 256px; transform: translate3d(1506px, 190px, 0px); opacity: 1;"><img alt="" src="./Geoportal Tabacay Aguilan_files/258(2).png" class="leaflet-tile leaflet-tile-loaded" style="width: 256px; height: 256px; transform: translate3d(2018px, 190px, 0px); opacity: 1;"><img alt="" src="./Geoportal Tabacay Aguilan_files/260(1).png" class="leaflet-tile leaflet-tile-loaded" style="width: 256px; height: 256px; transform: translate3d(1506px, 702px, 0px); opacity: 1;"><img alt="" src="./Geoportal Tabacay Aguilan_files/260(2).png" class="leaflet-tile leaflet-tile-loaded" style="width: 256px; height: 256px; transform: translate3d(2018px, 702px, 0px); opacity: 1;"><img alt="" src="./Geoportal Tabacay Aguilan_files/259(3).png" class="leaflet-tile leaflet-tile-loaded" style="width: 256px; height: 256px; transform: translate3d(1250px, 446px, 0px); opacity: 1;"><img alt="" src="./Geoportal Tabacay Aguilan_files/259(4).png" class="leaflet-tile leaflet-tile-loaded" style="width: 256px; height: 256px; transform: translate3d(2274px, 446px, 0px); opacity: 1;"><img alt="" src="./Geoportal Tabacay Aguilan_files/258(3).png" class="leaflet-tile leaflet-tile-loaded" style="width: 256px; height: 256px; transform: translate3d(1250px, 190px, 0px); opacity: 1;"><img alt="" src="./Geoportal Tabacay Aguilan_files/258(4).png" class="leaflet-tile leaflet-tile-loaded" style="width: 256px; height: 256px; transform: translate3d(2274px, 190px, 0px); opacity: 1;"><img alt="" src="./Geoportal Tabacay Aguilan_files/260(3).png" class="leaflet-tile leaflet-tile-loaded" style="width: 256px; height: 256px; transform: translate3d(1250px, 702px, 0px); opacity: 1;"><img alt="" src="./Geoportal Tabacay Aguilan_files/260(4).png" class="leaflet-tile leaflet-tile-loaded" style="width: 256px; height: 256px; transform: translate3d(2274px, 702px, 0px); opacity: 1;"><img alt="" src="./Geoportal Tabacay Aguilan_files/259(5).png" class="leaflet-tile leaflet-tile-loaded" style="width: 256px; height: 256px; transform: translate3d(994px, 446px, 0px); opacity: 1;"><img alt="" src="./Geoportal Tabacay Aguilan_files/259(6).png" class="leaflet-tile leaflet-tile-loaded" style="width: 256px; height: 256px; transform: translate3d(2530px, 446px, 0px); opacity: 1;"><img alt="" src="./Geoportal Tabacay Aguilan_files/258(5).png" class="leaflet-tile leaflet-tile-loaded" style="width: 256px; height: 256px; transform: translate3d(994px, 190px, 0px); opacity: 1;"><img alt="" src="./Geoportal Tabacay Aguilan_files/258(6).png" class="leaflet-tile leaflet-tile-loaded" style="width: 256px; height: 256px; transform: translate3d(2530px, 190px, 0px); opacity: 1;"><img alt="" src="./Geoportal Tabacay Aguilan_files/260(5).png" class="leaflet-tile leaflet-tile-loaded" style="width: 256px; height: 256px; transform: translate3d(994px, 702px, 0px); opacity: 1;"><img alt="" src="./Geoportal Tabacay Aguilan_files/260(6).png" class="leaflet-tile leaflet-tile-loaded" style="width: 256px; height: 256px; transform: translate3d(2530px, 702px, 0px); opacity: 1;"></div></div></div><div class="leaflet-pane leaflet-overlay-pane"></div><div class="leaflet-pane leaflet-shadow-pane"></div><div class="leaflet-pane leaflet-marker-pane"></div><div class="leaflet-pane leaflet-tooltip-pane"></div><div class="leaflet-pane leaflet-popup-pane"></div><div class="leaflet-proxy leaflet-zoom-animated" style="transform: translate3d(36731px, 66379.1px, 0px) scale(256);"></div></div><div class="leaflet-control-container"><div class="leaflet-top leaflet-left"></div><div class="leaflet-top leaflet-right"><div class="leaflet-control-layers leaflet-control" aria-haspopup="true"><a class="leaflet-control-layers-toggle" href="http://localhost:5173/#" title="Layers" role="button"></a><section class="leaflet-control-layers-list"><div class="leaflet-control-layers-base"><label><span><input type="radio" class="leaflet-control-layers-selector" name="leaflet-base-layers_69" checked="checked"><span> OpenStreetMap</span></span></label><label><span><input type="radio" class="leaflet-control-layers-selector" name="leaflet-base-layers_69"><span> Satellite</span></span></label><label><span><input type="radio" class="leaflet-control-layers-selector" name="leaflet-base-layers_69"><span> Topographic</span></span></label></div><div class="leaflet-control-layers-separator" style="display: none;"></div><div class="leaflet-control-layers-overlays"></div></section></div></div><div class="leaflet-bottom leaflet-left"><div class="leaflet-control-scale leaflet-control"><div class="leaflet-control-scale-line" style="width: 98px;">30 km</div><div class="leaflet-control-scale-line" style="width: 53px;">10 mi</div></div><div class="leaflet-control-zoom leaflet-bar leaflet-control"><a class="leaflet-control-zoom-in" href="http://localhost:5173/#" title="Zoom in" role="button" aria-label="Zoom in" aria-disabled="false"><span aria-hidden="true">+</span></a><a class="leaflet-control-zoom-out" href="http://localhost:5173/#" title="Zoom out" role="button" aria-label="Zoom out" aria-disabled="false"><span aria-hidden="true">−</span></a></div></div><div class="leaflet-bottom leaflet-right"><div class="leaflet-control-attribution leaflet-control"><a href="https://leafletjs.com/" title="A JavaScript library for interactive maps"><svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" class="leaflet-attribution-flag"><path fill="#4C7BE1" d="M0 0h12v4H0z"></path><path fill="#FFD500" d="M0 4h12v3H0z"></path><path fill="#E0BC00" d="M0 7h12v1H0z"></path></svg> Leaflet</a> <span aria-hidden="true">|</span> © <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors</div></div></div></div>

          <!-- Floating Action Menu -->
          <div class="floating-menu">
            <button id="layers-toggle" class="menu-button" title="Toggle Layers Panel">
              <i class="fas fa-layer-group"></i>
            </button>
            <button id="legend-toggle" class="menu-button" title="Toggle Legend Panel">
              <i class="fas fa-list"></i>
            </button>
            <button id="home-button" class="menu-button" title="Go to Ecuador">
              <i class="fas fa-home"></i>
            </button>
            <button id="search-toggle" class="menu-button" title="Search Locations">
              <i class="fas fa-search"></i>
            </button>
          </div>

          <!-- Search Panel -->
          <div id="search-panel" class="search-panel hidden" style="transform: translate3d(0px, 0px, 0px); right: auto;">
            <div class="search-header draggable-header">
              <input type="text" id="search-input" placeholder="Busca una ubicación...">
              <button id="search-close" class="close-button">
                <i class="fas fa-times"></i>
              </button>
            </div>
            <div id="search-results" class="search-results"></div>
          </div>

          <!-- Layers Panel -->
          <div id="sidebar" class="sidebar hidden" style="transform: translate3d(0px, 0px, 0px); right: auto;">
            <div class="sidebar-header draggable-header">
              <h2>Capas</h2>
              <button id="sidebar-close" class="close-button">
                <i class="fas fa-times"></i>
              </button>
            </div>
            <div id="layer-control" class="sidebar-content"><div class="layer-group" data-group-id="group_Delimitación" style="padding-left: 0px;"><div class="layer-group-header"><span class="layer-group-toggle collapsed">►</span><input type="checkbox" id="group-group_Delimitación"><label for="group-group_Delimitación">Delimitación</label></div><div class="layer-group-children" style="display: none;"><div class="layer-item" data-layer-id="geoportal_ws_00_DELIMITACION — Burgay_Alto_Tabacay_" style="padding-left: 15px;"><input type="checkbox" id="layer-geoportal_ws_00_DELIMITACION — Burgay_Alto_Tabacay_"><label for="layer-geoportal_ws_00_DELIMITACION — Burgay_Alto_Tabacay_">Burgay Alto Tabacay</label><span class="layer-info-icon" title="Show layer information" data-layer-id="geoportal_ws_00_DELIMITACION — Burgay_Alto_Tabacay_"><i class="fas fa-info-circle"></i></span></div><div class="layer-item" data-layer-id="geoportal_ws_00_DELIMITACION — Tabacay_" style="padding-left: 15px;"><input type="checkbox" id="layer-geoportal_ws_00_DELIMITACION — Tabacay_"><label for="layer-geoportal_ws_00_DELIMITACION — Tabacay_">Tabacay</label><span class="layer-info-icon" title="Show layer information" data-layer-id="geoportal_ws_00_DELIMITACION — Tabacay_"><i class="fas fa-info-circle"></i></span></div><div class="layer-item" data-layer-id="geoportal_ws_DELIMITACION Aguilan" style="padding-left: 15px;"><input type="checkbox" id="layer-geoportal_ws_DELIMITACION Aguilan"><label for="layer-geoportal_ws_DELIMITACION Aguilan">Aguilán</label><span class="layer-info-icon" title="Show layer information" data-layer-id="geoportal_ws_DELIMITACION Aguilan"><i class="fas fa-info-circle"></i></span></div></div></div><div class="layer-group" data-group-id="group_MICROCUENCAS_APORTANTES" style="padding-left: 0px;"><div class="layer-group-header"><span class="layer-group-toggle collapsed">►</span><input type="checkbox" id="group-group_MICROCUENCAS_APORTANTES"><label for="group-group_MICROCUENCAS_APORTANTES">MICROCUENCAS_APORTANTES</label></div><div class="layer-group-children" style="display: none;"><div class="layer-item" data-layer-id="geoportal_ws_01_AGUILAN — Aguilan_" style="padding-left: 15px;"><input type="checkbox" id="layer-geoportal_ws_01_AGUILAN — Aguilan_"><label for="layer-geoportal_ws_01_AGUILAN — Aguilan_">Aguilan</label></div><div class="layer-item" data-layer-id="geoportal_ws_02_CONDORYACU_ROSARIO — Condoryacu_Rosario" style="padding-left: 15px;"><input type="checkbox" id="layer-geoportal_ws_02_CONDORYACU_ROSARIO — Condoryacu_Rosario"><label for="layer-geoportal_ws_02_CONDORYACU_ROSARIO — Condoryacu_Rosario">Condoryacu_Rosario</label></div><div class="layer-item" data-layer-id="geoportal_ws_03_MAPAYACU — MAPAYACU_" style="padding-left: 15px;"><input type="checkbox" id="layer-geoportal_ws_03_MAPAYACU — MAPAYACU_"><label for="layer-geoportal_ws_03_MAPAYACU — MAPAYACU_">MAPAYACU</label></div><div class="layer-item" data-layer-id="geoportal_ws_04_NUDPUD — NUDPUD_" style="padding-left: 15px;"><input type="checkbox" id="layer-geoportal_ws_04_NUDPUD — NUDPUD_"><label for="layer-geoportal_ws_04_NUDPUD — NUDPUD_">NUDPUD</label></div><div class="layer-item" data-layer-id="geoportal_ws_05_LLAUCAY — Llaucay" style="padding-left: 15px;"><input type="checkbox" id="layer-geoportal_ws_05_LLAUCAY — Llaucay"><label for="layer-geoportal_ws_05_LLAUCAY — Llaucay">Llaucay</label></div><div class="layer-item" data-layer-id="geoportal_ws_06_TABACAY — TABACAY_" style="padding-left: 15px;"><input type="checkbox" id="layer-geoportal_ws_06_TABACAY — TABACAY_"><label for="layer-geoportal_ws_06_TABACAY — TABACAY_">TABACAY_</label></div><div class="layer-item" data-layer-id="geoportal_ws_07_MICROCUENCAS_APORTANTES_TABACAY_AGUILAN — Microcuenca_apor" style="padding-left: 15px;"><input type="checkbox" id="layer-geoportal_ws_07_MICROCUENCAS_APORTANTES_TABACAY_AGUILAN — Microcuenca_apor"><label for="layer-geoportal_ws_07_MICROCUENCAS_APORTANTES_TABACAY_AGUILAN — Microcuenca_apor">MICROCUENCAS_APORTANTES_TABACAY_AGUILAN</label></div></div></div><div class="layer-group" data-group-id="group_Areas conservación" style="padding-left: 0px;"><div class="layer-group-header"><span class="layer-group-toggle collapsed">►</span><input type="checkbox" id="group-group_Areas conservación"><label for="group-group_Areas conservación">Areas conservación</label></div><div class="layer-group-children" style="display: none;"><div class="layer-item" data-layer-id="geoportal_ws_01_BVP_CUBILAN — BVP_CUBILAN" style="padding-left: 15px;"><input type="checkbox" id="layer-geoportal_ws_01_BVP_CUBILAN — BVP_CUBILAN"><label for="layer-geoportal_ws_01_BVP_CUBILAN — BVP_CUBILAN">BVP_CUBILAN</label></div><div class="layer-item" data-layer-id="geoportal_ws_03_APH — APH" style="padding-left: 15px;"><input type="checkbox" id="layer-geoportal_ws_03_APH — APH"><label for="layer-geoportal_ws_03_APH — APH">APH</label></div><div class="layer-item" data-layer-id="geoportal_ws_04_Z_Intangible_Pugioloma — Z_Intangible_Pugioloma" style="padding-left: 15px;"><input type="checkbox" id="layer-geoportal_ws_04_Z_Intangible_Pugioloma — Z_Intangible_Pugioloma"><label for="layer-geoportal_ws_04_Z_Intangible_Pugioloma — Z_Intangible_Pugioloma">Z_Intangible_Pugioloma</label></div><div class="layer-group" data-group-id="group_Areas conservación_Zona_Intangible_Molobog" style="padding-left: 15px;"><div class="layer-group-header"><span class="layer-group-toggle collapsed">►</span><input type="checkbox" id="group-group_Areas conservación_Zona_Intangible_Molobog"><label for="group-group_Areas conservación_Zona_Intangible_Molobog">Zona_Intangible_Molobog</label></div><div class="layer-group-children" style="display: none;"><div class="layer-item" data-layer-id="geoportal_ws_05_Zona_Intangible_Molobog — Bosque_area_Molobog" style="padding-left: 30px;"><input type="checkbox" id="layer-geoportal_ws_05_Zona_Intangible_Molobog — Bosque_area_Molobog"><label for="layer-geoportal_ws_05_Zona_Intangible_Molobog — Bosque_area_Molobog">Bosque_area_Molobog</label></div><div class="layer-item" data-layer-id="geoportal_ws_05_Zona_Intangible_Molobog — Paramo_area_Molobog" style="padding-left: 30px;"><input type="checkbox" id="layer-geoportal_ws_05_Zona_Intangible_Molobog — Paramo_area_Molobog"><label for="layer-geoportal_ws_05_Zona_Intangible_Molobog — Paramo_area_Molobog">Paramo_area_Molobog</label></div><div class="layer-item" data-layer-id="geoportal_ws_05_Zona_Intangible_Molobog — Pastizal_area_Molobog_" style="padding-left: 30px;"><input type="checkbox" id="layer-geoportal_ws_05_Zona_Intangible_Molobog — Pastizal_area_Molobog_"><label for="layer-geoportal_ws_05_Zona_Intangible_Molobog — Pastizal_area_Molobog_">Pastizal_area_Molobog_</label></div></div></div><div class="layer-item" data-layer-id="geoportal_ws_05_Zona_Intangible_Molobog — Zona_Intangible_Molobog" style="padding-left: 15px;"><input type="checkbox" id="layer-geoportal_ws_05_Zona_Intangible_Molobog — Zona_Intangible_Molobog"><label for="layer-geoportal_ws_05_Zona_Intangible_Molobog — Zona_Intangible_Molobog">Zona_Intangible_Molobog</label></div><div class="layer-item" data-layer-id="geoportal_ws_06_KBA_Montañas_Cañar — KBA_" style="padding-left: 15px;"><input type="checkbox" id="layer-geoportal_ws_06_KBA_Montañas_Cañar — KBA_"><label for="layer-geoportal_ws_06_KBA_Montañas_Cañar — KBA_">KBA_Montañas_Cañar</label></div></div></div><div class="layer-group" data-group-id="group_Cobertura_Vegetal" style="padding-left: 0px;"><div class="layer-group-header"><span class="layer-group-toggle collapsed">►</span><input type="checkbox" id="group-group_Cobertura_Vegetal"><label for="group-group_Cobertura_Vegetal">Cobertura_Vegetal</label></div><div class="layer-group-children" style="display: none;"><div class="layer-item" data-layer-id="geoportal_ws_01_Cobertura_Vegetal_2008_DH — Cobertura_V_2008_DH" style="padding-left: 15px;"><input type="checkbox" id="layer-geoportal_ws_01_Cobertura_Vegetal_2008_DH — Cobertura_V_2008_DH"><label for="layer-geoportal_ws_01_Cobertura_Vegetal_2008_DH — Cobertura_V_2008_DH">Cobertura_Vegetal_2008_DH</label></div><div class="layer-item" data-layer-id="geoportal_ws_02_Cobertura_Vegetal_2023_DP — Cobertura_Vegetal_2023_DP" style="padding-left: 15px;"><input type="checkbox" id="layer-geoportal_ws_02_Cobertura_Vegetal_2023_DP — Cobertura_Vegetal_2023_DP"><label for="layer-geoportal_ws_02_Cobertura_Vegetal_2023_DP — Cobertura_Vegetal_2023_DP">Cobertura_Vegetal_2023_DP</label></div></div></div><div class="layer-group" data-group-id="group_Red Hídrica" style="padding-left: 0px;"><div class="layer-group-header"><span class="layer-group-toggle collapsed">►</span><input type="checkbox" id="group-group_Red Hídrica"><label for="group-group_Red Hídrica">Red Hídrica</label></div><div class="layer-group-children" style="display: none;"><div class="layer-item" data-layer-id="geoportal_ws_01_Red_Hidrica — 01_Cursos_agua_DH" style="padding-left: 15px;"><input type="checkbox" id="layer-geoportal_ws_01_Red_Hidrica — 01_Cursos_agua_DH"><label for="layer-geoportal_ws_01_Red_Hidrica — 01_Cursos_agua_DH">Cursos_agua_DH</label></div><div class="layer-item" data-layer-id="geoportal_ws_01_Red_Hidrica — RED_HIDRICA" style="padding-left: 15px;"><input type="checkbox" id="layer-geoportal_ws_01_Red_Hidrica — RED_HIDRICA"><label for="layer-geoportal_ws_01_Red_Hidrica — RED_HIDRICA">Red Hídrica</label></div><div class="layer-item" data-layer-id="geoportal_ws_Cauce_principal" style="padding-left: 15px;"><input type="checkbox" id="layer-geoportal_ws_Cauce_principal"><label for="layer-geoportal_ws_Cauce_principal">Cauce principal</label></div><div class="layer-item" data-layer-id="geoportal_ws_Rios_principales_ — Rios_principales" style="padding-left: 15px;"><input type="checkbox" id="layer-geoportal_ws_Rios_principales_ — Rios_principales"><label for="layer-geoportal_ws_Rios_principales_ — Rios_principales">Ríos principales</label><span class="layer-info-icon" title="Show layer information" data-layer-id="geoportal_ws_Rios_principales_ — Rios_principales"><i class="fas fa-info-circle"></i></span></div></div></div><div class="layer-group" data-group-id="group_División política" style="padding-left: 0px;"><div class="layer-group-header"><span class="layer-group-toggle collapsed">►</span><input type="checkbox" id="group-group_División política"><label for="group-group_División política">División política</label></div><div class="layer-group-children" style="display: none;"><div class="layer-item" data-layer-id="geoportal_ws_Limite_parroquial_2022_DH_" style="padding-left: 15px;"><input type="checkbox" id="layer-geoportal_ws_Limite_parroquial_2022_DH_"><label for="layer-geoportal_ws_Limite_parroquial_2022_DH_">Limite_parroquial_2022_DH</label></div><div class="layer-item" data-layer-id="geoportal_ws_Limite_parroquial_2022_DP" style="padding-left: 15px;"><input type="checkbox" id="layer-geoportal_ws_Limite_parroquial_2022_DP"><label for="layer-geoportal_ws_Limite_parroquial_2022_DP">Limite_parroquial_2022_DP</label></div></div></div><div class="layer-group" data-group-id="group_Raster Hidrología" style="padding-left: 0px;"><div class="layer-group-header"><span class="layer-group-toggle collapsed">►</span><input type="checkbox" id="group-group_Raster Hidrología"><label for="group-group_Raster Hidrología">Raster Hidrología</label></div><div class="layer-group-children" style="display: none;"><div class="layer-item" data-layer-id="geoportal_ws_RasterHidro" style="padding-left: 15px;"><input type="checkbox" id="layer-geoportal_ws_RasterHidro"><label for="layer-geoportal_ws_RasterHidro">MDT</label><span class="layer-info-icon" title="Show layer information" data-layer-id="geoportal_ws_RasterHidro"><i class="fas fa-info-circle"></i></span></div></div></div></div>
          </div>
        </div>

        <!-- Legend Panel -->
        <div id="legend-panel" class="legend-panel hidden" style="transform: translate3d(0px, 0px, 0px); right: auto;">
          <div class="legend-header draggable-header">
            <h2>Leyenda</h2>
            <button id="legend-close" class="close-button">
              <i class="fas fa-times"></i>
            </button>
          </div>
          <div id="legend-content" class="legend-content"></div>
        </div>
      </div>

      <!-- Footer -->
      <footer class="geoportal-footer">
        <div class="footer-content">
          <p class="footer-project-text">
            Geoportal para el proyecto "DESARROLLO DEL PLAN DE MANEJO PARA LA MICROCUENCA DE LOS RÍOS TABACAY Y AGUILÁN, EN LA CIUDAD DE AZOGUES"
          </p>
          <p class="footer-rights">
            Todos los derechos reservados
            <a href="https://www.ucacue.edu.ec/" target="_blank" rel="noopener noreferrer" class="footer-link">UCACUE</a>
            x
            <a href="https://www.emapal.gob.ec/" target="_blank" rel="noopener noreferrer" class="footer-link">EMAPAL EP</a>
          </p>
        </div>
      </footer>
    </div>
    <script type="module" src="./Geoportal Tabacay Aguilan_files/main.ts"></script>
  

<div class="layer-tooltip" id="tooltip-geoportal_ws_00_DELIMITACION — Burgay_Alto_Tabacay_" style="display: none;">Delimitación de Burgay Alto Tabacay</div><div class="layer-tooltip" id="tooltip-geoportal_ws_00_DELIMITACION — Tabacay_" style="display: none;">Delimitación de Tabacay</div><div class="layer-tooltip" id="tooltip-geoportal_ws_DELIMITACION Aguilan" style="display: none;">Delimitación de Aguilán</div><div class="layer-tooltip" id="tooltip-geoportal_ws_Rios_principales_ — Rios_principales" style="display: none;">Ríos principales</div><div class="layer-tooltip" id="tooltip-geoportal_ws_RasterHidro" style="display: none;">Raster Hidrología de MDT</div></body></html>